<template>
  <div class="asset-change-list-page">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">资产变更管理</h2>
        <div class="header-actions">
          <el-button @click="refreshData" :loading="loading" size="default">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="变更ID">
          <el-input 
            v-model="searchForm.asset_change_id" 
            placeholder="请输入变更ID"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        
        <el-form-item label="资产ID">
          <el-input 
            v-model="searchForm.asset_id" 
            placeholder="请输入资产ID"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        
        <el-form-item label="变更日期">
          <el-date-picker
            v-model="searchForm.change_date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item label="制单人">
          <el-input 
            v-model="searchForm.creator_name" 
            placeholder="请输入制单人"
            clearable
            style="width: 120px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 变更列表表格 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        v-loading="loading" 
        border 
        style="width: 100%"
        :default-sort="{ prop: 'createdAt', order: 'descending' }"
        empty-text="暂无变更记录"
        @sort-change="handleSortChange"
      >
        <!-- 变更ID -->
        <el-table-column prop="asset_change_id" label="变更ID" width="140" fixed sortable>
          <template #default="{ row }">
            <el-button link type="primary" @click="goToDetail(row.id)" class="change-id-link">
              {{ row.asset_change_id }}
            </el-button>
          </template>
        </el-table-column>

        <!-- 资产ID -->
        <el-table-column prop="asset.asset_id" label="资产ID" width="140">
          <template #default="{ row }">
            <el-button 
              link 
              type="success" 
              @click="goToAssetDetail(row.asset?.id)"
              v-if="row.asset"
            >
              {{ row.asset.asset_id }}
            </el-button>
            <span v-else class="no-data">N/A</span>
          </template>
        </el-table-column>

        <!-- 变更日期 -->
        <el-table-column prop="change_date" label="变更日期" width="120" sortable>
          <template #default="{ row }">
            {{ formatDate(row.change_date) }}
          </template>
        </el-table-column>

        <!-- 制单人 -->
        <el-table-column prop="creator.name" label="制单人" width="100">
          <template #default="{ row }">
            {{ getCreatorName(row.creator) }}
          </template>
        </el-table-column>

        <!-- 变更摘要 -->
        <el-table-column label="变更摘要" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="change-summary">
              <!-- 显示完整的变更信息，不再显示重复的标签 -->
              <div class="summary-text">
                {{ getChangeSummary(row.remark) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 关联订单数 -->
        <el-table-column label="关联订单" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              v-if="row.related_orders_count > 0" 
              type="success" 
              size="small"
            >
              {{ row.related_orders_count }}个
            </el-tag>
            <span v-else class="no-data">无</span>
          </template>
        </el-table-column>
        
        <!-- 创建时间 -->
        <el-table-column prop="createdAt" label="创建时间" width="160" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="warning"
              @click="handleRollback(row)"
            >
              回滚
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 回滚确认对话框 -->
    <RollbackDialog
      v-model="showRollbackDialog"
      :rollback-record="rollbackRecord"
      :loading="rollbackLoading"
      @confirm="confirmRollback"
      @cancel="cancelRollback"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format.js'
import { getAllAssetChanges } from '@/api/asset.js'
import { useAssetData } from '../composables/useAssetData.js'
import RollbackDialog from '../components/RollbackDialog.vue'

const router = useRouter()

// 使用 composable
const {
  rollbackLoading,
  showRollbackDialog,
  rollbackRecord,
  showRollbackConfirm,
  executeRollback,
  cancelRollback
} = useAssetData()

// 状态数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索表单
const searchForm = ref({
  asset_change_id: '',
  asset_id: '',
  change_date_range: null,
  creator_name: ''
})

// 排序参数
const sortParams = ref({
  prop: 'createdAt',
  order: 'descending'
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm.value,
      sortBy: sortParams.value.prop,
      sortOrder: sortParams.value.order === 'ascending' ? 'ASC' : 'DESC'
    }
    
    // 处理日期范围
    if (searchForm.value.change_date_range && searchForm.value.change_date_range.length === 2) {
      params.change_date_start = searchForm.value.change_date_range[0]
      params.change_date_end = searchForm.value.change_date_range[1]
    }
    
    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getAllAssetChanges(params)
    tableData.value = response.records || response || []
    total.value = response.total || response?.length || 0
  } catch (error) {
    console.error('获取变更列表失败:', error)
    ElMessage.error('获取变更列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  currentPage.value = 1
  loadData()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    asset_change_id: '',
    asset_id: '',
    change_date_range: null,
    creator_name: ''
  }
  currentPage.value = 1
  loadData()
}

// 排序处理
const handleSortChange = ({ prop, order }) => {
  sortParams.value = { prop, order }
  loadData()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 页面跳转
const goToDetail = (changeId) => {
  router.push({ name: 'asset-change-detail', params: { id: changeId } })
}

const goToAssetDetail = (assetId) => {
  if (assetId) {
    router.push({ name: 'asset-detail', params: { id: assetId } })
  }
}

// 回滚处理
let currentAssetId = null // 存储当前要回滚的资产ID

const handleRollback = (row) => {
  // 存储资产ID
  currentAssetId = row.asset?.id

  // 构造回滚记录对象
  const record = {
    id: row.id,
    asset_change_id: row.asset_change_id,
    created_at: row.change_date,
    created_by: row.creator?.name || '未知'
  }

  showRollbackConfirm(record)
}

const confirmRollback = async () => {
  await executeRollback(currentAssetId, async () => {
    await loadData() // 重新加载数据
  })
}

// 工具函数
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

const getCreatorName = (creator) => {
  if (!creator) return '未知'
  return creator.name || creator.employee_name || '未知'
}

const getChangeSummary = (remark) => {
  if (!remark) return '无备注'

  const lines = remark.split('\n')

  // 查找包含"本次变更涉及以下字段："的行
  const changeInfoLine = lines.find(line => line.includes('本次变更涉及以下字段：'))

  if (changeInfoLine) {
    // 提取变更信息部分，去掉"本次变更涉及以下字段："前缀
    const changeInfo = changeInfoLine.replace('本次变更涉及以下字段：', '').trim()

    // 如果有用户手工输入的备注（第一行），则组合显示
    const userRemark = lines[0].trim()
    if (userRemark && !userRemark.includes('本次变更涉及以下字段：')) {
      return `${userRemark} | ${changeInfo}`
    }

    // 只有自动生成的变更信息
    return changeInfo
  }

  // 没有自动生成的变更信息，显示第一行
  const firstLine = lines[0]
  if (firstLine.length > 100) {
    return firstLine.substring(0, 100) + '...'
  }

  return firstLine
}



// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.asset-change-list-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(240, 147, 251, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 搜索栏 */
.search-bar {
  margin-bottom: 20px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-bar .el-form {
  margin-bottom: 0;
}

.search-bar .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 变更ID链接 */
.change-id-link {
  font-weight: 600;
  font-size: 14px;
}

/* 变更摘要 */
.change-summary {
  max-width: 300px;
}

.summary-text {
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.change-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.field-tag {
  font-size: 12px;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .search-bar .el-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .search-bar .el-form-item {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .asset-change-list-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .search-bar {
    padding: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
