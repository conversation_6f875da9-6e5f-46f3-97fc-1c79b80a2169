const db = require('../models');
const { Op } = require('sequelize'); // 引入Op，用于复杂查询
const { generateAssetId, generateAssetChangeId } = require('../utils/id_helper'); // 引入新函数
const AssetFilterService = require('../services/assetFilter.service');

const Asset = db.Asset;
const AssetChangeLog = db.AssetChangeLog;
const User = db.User;
const Enterprise = db.Enterprise;
const Product = db.Product;
const ProductFeature = db.ProductFeature;
const Employee = db.Employee;
const OrderHead = db.OrderHead;

/**
 * [新增] 获取下一个可用的资产ID
 */
exports.getNextAssetId = async (req, res) => {
  try {
    const nextId = await generateAssetId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个资产ID时出错:', error);
    res.status(500).json({ message: '生成资产ID失败', error: error.message });
  }
};



/**
 * [新增] 获取下一个可用的资产变更ID
 */
exports.getNextAssetChangeId = async (req, res) => {
    try {
        const nextId = await generateAssetChangeId();
        res.status(200).json({ next_id: nextId });
    } catch (error) {
        console.error('获取下一个资产变更ID时出错:', error);
        res.status(500).json({ message: '生成资产变更ID失败', error: error.message });
    }
};

// 创建新资产
exports.createAsset = async (req, res) => {
  try {
    // [重构] 统一从 asset_data 中获取数据
    const assetData = req.body.asset_data;
    const relatedOrderIds = req.body.related_order_ids || []; // 获取关联订单ID列表

    if (!assetData) {
        return res.status(400).json({ message: '创建资产时必须提供 asset_data 字段。' });
    }

    if (!assetData.asset_id) {
        return res.status(400).json({ message: '创建资产时必须提供资产ID (asset_id)。' });
    }

    // [新增] 验证必填字段
    const requiredFields = ['enterprise_id', 'product_id', 'user_count', 'account_count'];
    const missingFields = requiredFields.filter(field => !assetData[field] && assetData[field] !== 0);
    if (missingFields.length > 0) {
        return res.status(400).json({
            message: `创建资产时缺少必填字段: ${missingFields.join(', ')}`,
            missingFields: missingFields
        });
    }

    // [重构] 严格进行唯一性校验
    const existingAsset = await Asset.findOne({ where: { asset_id: assetData.asset_id } });
    if (existingAsset) {
      return res.status(409).json({ message: `资产ID '${assetData.asset_id}' 已存在。` });
    }

    // [新增] 添加创建人信息
    const finalAssetData = {
      ...assetData,
      creator_id: req.user?.id // 从JWT token中获取当前用户ID
    };

    // 使用事务确保数据一致性
    const t = await db.sequelize.transaction();
    try {
      // 创建资产
      const newAsset = await Asset.create(finalAssetData, { transaction: t });

      // [新增] 处理关联订单：将选中的订单绑定到新资产
      if (relatedOrderIds && relatedOrderIds.length > 0) {
        await OrderHead.update(
          { asset_id: newAsset.id },
          {
            where: {
              id: { [Op.in]: relatedOrderIds },
              asset_id: null // 只更新未绑定资产的订单
            },
            transaction: t
          }
        );
      }

      await t.commit();
      res.status(201).json(newAsset);
    } catch (error) {
      await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error("创建资产失败:", error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: '数据验证失败',
        errors: error.errors.map(e => ({
          field: e.path,
          message: e.message,
          value: e.value
        }))
      });
    }
    res.status(500).json({ message: '创建资产失败', error: error.message });
  }
};

// 获取所有资产（支持分页和详细过滤）
exports.getAllAssets = async (req, res) => {
    try {
        const { page = 1, pageSize = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = req.query;

        // 使用过滤服务构建查询条件
        const assetWhere = AssetFilterService.buildAssetWhereCondition(req.query);
        const productWhere = AssetFilterService.buildProductWhereCondition(req.query);
        const enterpriseWhere = AssetFilterService.buildEnterprisePermissionFilter(req.user);
        const { offset, limit } = AssetFilterService.buildPaginationParams(page, pageSize);
        const order = AssetFilterService.buildOrderCondition(sortBy, sortOrder);




        const queryOptions = {
            where: assetWhere,
            include: [
                {
                    model: Enterprise,
                    as: 'enterprise',
                    where: (Object.keys(enterpriseWhere).length > 0 || Object.getOwnPropertySymbols(enterpriseWhere).length > 0) ? enterpriseWhere : undefined,
                    required: (Object.keys(enterpriseWhere).length > 0 || Object.getOwnPropertySymbols(enterpriseWhere).length > 0)
                },
                {
                    model: Product,
                    as: 'product',
                    where: (Object.keys(productWhere).length > 0 || Object.getOwnPropertySymbols(productWhere).length > 0) ? productWhere : undefined,
                    required: (Object.keys(productWhere).length > 0 || Object.getOwnPropertySymbols(productWhere).length > 0) // 如果有产品过滤条件，则必须匹配
                },
                { model: User, as: 'user', required: false },
                { model: Employee, as: 'creator', required: false }
            ],
            order,
            offset,
            limit,
            distinct: true // 确保计数正确
        };



        const { count, rows } = await Asset.findAndCountAll(queryOptions);

        res.status(200).json({
            records: rows,
            total: count,
            page: parseInt(page),
            pageSize: limit
        });
    } catch (error) {
        console.error("获取资产列表失败:", error);
        res.status(500).json({ message: "获取资产列表失败", error: error.message });
    }
};

// 获取单个资产详情，包括变更记录和关联订单
exports.getAssetById = async (req, res) => {
    try {
        const { id } = req.params;

        // 优化查询：分步获取数据，避免复杂JOIN和大字段导致的内存问题
        const asset = await Asset.findByPk(id, {
            include: [
                {
                    model: User,
                    as: 'user',
                    attributes: ['id', 'user_id', 'name', 'nickname', 'mobile', 'email'] // 精简字段
                },
                {
                    model: Enterprise,
                    as: 'enterprise',
                    attributes: ['id', 'enterprise_id', 'name', 'contact_person', 'contact_phone'] // 精简字段
                },
                {
                    model: Product,
                    as: 'product',
                    attributes: ['id', 'product_id', 'product_name', 'version_name', 'base_price'], // 精简字段
                    include: [
                        {
                            model: ProductFeature,
                            as: 'features',
                            attributes: ['id', 'feature_id', 'feature_name', 'description'],
                            through: {
                                attributes: ['id', 'feature_price', 'remark']
                            }
                        }
                    ]
                },
                {
                    model: Employee,
                    as: 'creator',
                    attributes: ['id', 'employee_number', 'name', 'mobile', 'department'] // 精简字段
                }
            ]
        });

        if (!asset) {
            return res.status(404).json({ message: '未找到指定资产' });
        }

        // 单独查询变更记录，排除大字段，限制数量
        const changeLogs = await AssetChangeLog.findAll({
            where: { asset_id: id },
            attributes: [
                'id', 'asset_change_id', 'change_date', 'asset_id',
                'remark', 'related_order_ids', 'creator_id', 'createdAt', 'updatedAt'
                // 排除 snapshot_before 和 snapshot_after 大字段
            ],
            include: [
                {
                    model: Employee,
                    as: 'creator',
                    attributes: ['id', 'name', 'mobile'] // 精简字段
                }
            ],
            order: [['change_date', 'DESC'], ['createdAt', 'DESC']],
            limit: 20 // 限制变更记录数量，避免一次性加载过多数据
        });

        // 单独查询关联订单，精简字段
        const orders = await OrderHead.findAll({
            where: { asset_id: id },
            attributes: [
                'id', 'order_id', 'order_category', 'order_type', 'actual_amount',
                'payment_status', 'audit_status', 'created_at', 'remark'
            ],
            include: [
                {
                    model: Employee,
                    as: 'creator',
                    attributes: ['id', 'name']
                }
            ],
            order: [['created_at', 'DESC']],
            limit: 10 // 限制订单数量
        });

        // 组装返回数据
        const result = {
            ...asset.toJSON(),
            changeLogs,
            orders
        };

        res.status(200).json(result);
    } catch (error) {
        console.error('获取资产详情时出错:', error);
        res.status(500).json({ message: '获取资产详情失败', error: error.message });
    }
};

// "修改"资产 (普通更新)
exports.updateAsset = async (req, res) => {
  try {
    const { id } = req.params;
    const assetData = req.body;

    // [新增] 验证 asset_id 唯一性
    if (assetData.asset_id) {
      const existing = await Asset.findOne({ where: { asset_id: assetData.asset_id, id: { [Op.ne]: id } } });
      if (existing) {
        return res.status(409).json({ message: `资产ID '${assetData.asset_id}' 已被其他资产占用。` });
      }
    }

    const asset = await Asset.findByPk(id);
    if (!asset) {
      return res.status(404).json({ message: '未找到指定资产' });
    }
    await asset.update(assetData);
    res.status(200).json(asset);
  } catch (error) {
    console.error('更新资产时出错:', error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '更新资产失败', error: error.message });
  }
};

// "回滚"资产变更
exports.revertAssetChange = async (req, res) => {
    const { changeId } = req.params; // 注意：这里我们用changeId来操作
    const t = await db.sequelize.transaction();
    try {
        const log = await AssetChangeLog.findByPk(changeId, { transaction: t });
        if (!log) {
            await t.rollback();
            return res.status(404).json({ message: '未找到该变更记录' });
        }

        // 1. 从快照中恢复数据
        const snapshotBefore = log.snapshot_before;
        const asset = await Asset.findByPk(log.asset_id, { transaction: t });
        if (!asset) {
            await t.rollback();
            return res.status(404).json({ message: '未找到与记录关联的资产' });
        }
        await asset.update(snapshotBefore, { transaction: t });

        // 2. 删除此条变更记录
        await log.destroy({ transaction: t });

        await t.commit();
        res.status(200).json({ message: '变更已成功回滚', asset });

    } catch (error) {
        await t.rollback();
        console.error('回滚资产变更时出错:', error);
        res.status(500).json({ message: '回滚失败', error: error.message });
    }
};




/**
 * [新增] 根据资产ID获取其所有变更记录
 */
exports.getChangesByAssetId = async (req, res) => {
  try {
    const { assetId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 优化查询：排除大字段
    const { count, rows } = await AssetChangeLog.findAndCountAll({
      where: { asset_id: assetId },
      attributes: [
        'id', 'asset_change_id', 'change_date', 'asset_id',
        'remark', 'related_order_ids', 'creator_id', 'createdAt', 'updatedAt'
        // 排除 snapshot_before 和 snapshot_after 大字段
      ],
      include: [{
        model: Employee,
        as: 'creator',
        attributes: ['id', 'name']
      }],
      order: [['change_date', 'DESC'], ['createdAt', 'DESC']],
      offset,
      limit
    });

    res.status(200).json({
      records: rows,
      total: count,
      page: parseInt(page),
      pageSize: limit
    });
  } catch (error) {
    console.error('获取资产变更记录时出错:', error);
    res.status(500).json({ message: '获取资产变更记录失败', error: error.message });
  }
};

/**
 * [新增] 创建资产变更记录 (新的API接口)
 */
exports.createAssetChange = async (req, res) => {
    try {
        const changeData = req.body;

        // 验证必填字段
        if (!changeData.asset_change_id) {
            return res.status(400).json({ message: '必须提供资产变更ID (asset_change_id)。' });
        }

        if (!changeData.asset_id) {
            return res.status(400).json({ message: '必须提供资产ID (asset_id)。' });
        }

        // 验证变更ID唯一性
        const existingChange = await AssetChangeLog.findOne({
            where: { asset_change_id: changeData.asset_change_id }
        });
        if (existingChange) {
            return res.status(409).json({
                message: `资产变更ID '${changeData.asset_change_id}' 已存在。`
            });
        }

        // 验证资产是否存在
        const asset = await Asset.findByPk(changeData.asset_id);
        if (!asset) {
            return res.status(404).json({ message: '未找到指定资产' });
        }

        const t = await db.sequelize.transaction();
        try {
            // 更新资产数据
            if (changeData.snapshot_after) {
                await asset.update(changeData.snapshot_after, { transaction: t });
            }

            // 创建变更记录
            const newChange = await AssetChangeLog.create({
                asset_change_id: changeData.asset_change_id,
                change_date: changeData.change_date || new Date(),
                asset_id: parseInt(changeData.asset_id, 10),
                snapshot_before: changeData.snapshot_before || {},
                snapshot_after: changeData.snapshot_after || {},
                remark: changeData.remark || '',
                related_order_ids: changeData.change_order_ids || [], // 保存关联的订单ID列表
                creator_id: changeData.creator?.id || req.user?.id, // 从请求数据或JWT token获取
            }, { transaction: t });

            await t.commit();
            res.status(201).json({
                message: '资产变更记录创建成功',
                change: newChange
            });

        } catch (error) {
            await t.rollback();
            throw error;
        }

    } catch (error) {
        console.error('创建资产变更记录时出错:', error);
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                message: '数据验证失败',
                errors: error.errors.map(e => e.message)
            });
        }
        res.status(500).json({
            message: '创建资产变更记录失败',
            error: error.message
        });
    }
};

/**
 * [新增] 获取所有资产变更记录列表
 */
exports.getAllAssetChanges = async (req, res) => {
  try {
    const { page = 1, pageSize = 20, sortBy = 'change_date', sortOrder = 'DESC' } = req.query;

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 处理排序字段映射
    let orderField = sortBy;
    if (sortBy === 'createdAt') {
      orderField = 'createdAt'; // AssetChangeLog 使用 createdAt
    }

    // 优化查询：排除大字段，精简关联查询
    const { count, rows } = await AssetChangeLog.findAndCountAll({
      attributes: [
        'id', 'asset_change_id', 'change_date', 'asset_id',
        'remark', 'related_order_ids', 'creator_id', 'createdAt', 'updatedAt'
        // 排除 snapshot_before 和 snapshot_after 大字段
      ],
      include: [
        {
          model: Employee,
          as: 'creator',
          attributes: ['id', 'name'] // 保持精简
        },
        {
          model: Asset,
          as: 'asset',
          attributes: ['id', 'asset_id'], // 保持精简
          include: [
            {
              model: Enterprise,
              as: 'enterprise',
              attributes: ['id', 'name'] // 保持精简
            },
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_name', 'version_name'] // 保持精简
            }
          ]
        }
      ],
      order: [[orderField, sortOrder.toUpperCase()]],
      offset,
      limit,
      distinct: true // 确保计数正确
    });

    res.status(200).json({
      records: rows,
      total: count,
      page: parseInt(page),
      pageSize: limit
    });
  } catch (error) {
    console.error('获取所有资产变更记录时出错:', error);
    res.status(500).json({ message: '获取所有资产变更记录失败', error: error.message });
  }
};

/**
 * [新增] 获取资产的最新变更记录
 */
exports.getLatestAssetChange = async (req, res) => {
  try {
    const { assetId } = req.params;

    const latestChange = await AssetChangeLog.findOne({
      where: { asset_id: assetId },
      attributes: ['id', 'asset_change_id', 'change_date', 'createdAt'],
      order: [['change_date', 'DESC'], ['createdAt', 'DESC']],
      limit: 1
    });

    if (!latestChange) {
      return res.status(404).json({ message: '该资产暂无变更记录' });
    }

    res.status(200).json(latestChange);
  } catch (error) {
    console.error('获取最新变更记录失败:', error);
    res.status(500).json({ message: '获取最新变更记录失败', error: error.message });
  }
};

/**
 * [新增] 根据变更记录ID获取单个变更记录详情（包含完整快照数据）
 */
exports.getAssetChangeById = async (req, res) => {
  try {
    const { changeId } = req.params;

    // 这个方法专门用于查看变更记录详情，包含完整的快照数据
    // 由于是单条记录查询，不会有排序内存问题
    const change = await AssetChangeLog.findByPk(changeId, {
      include: [
        {
          model: Employee,
          as: 'creator',
          attributes: ['id', 'name']
        },
        {
          model: Asset,
          as: 'asset',
          attributes: ['id', 'asset_id'],
          include: [
            {
              model: Enterprise,
              as: 'enterprise',
              attributes: ['id', 'name']
            },
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_name', 'version_name']
            }
          ]
        }
      ]
    });

    if (!change) {
      return res.status(404).json({ message: '未找到指定的变更记录' });
    }

    // 获取关联的订单信息
    let relatedOrders = [];
    if (change.related_order_ids && change.related_order_ids.length > 0) {
      // 根据保存的订单ID列表获取订单
      relatedOrders = await OrderHead.findAll({
        where: {
          id: {
            [Op.in]: change.related_order_ids
          }
        },
        include: [
          {
            model: Employee,
            as: 'creator',
            attributes: ['id', 'name']
          }
        ],
        attributes: [
          'id', 'order_id', 'created_at', 'payment_status', 'order_type', 'order_category', 'audit_status', 'remark',
          ['actual_amount', 'total_amount'], // 将 actual_amount 字段别名为 total_amount
          'standard_amount', 'tax_amount'
        ],
        order: [['created_at', 'DESC']]
      });
    }

    // 将关联订单信息添加到响应中
    const responseData = {
      ...change.toJSON(),
      related_orders: relatedOrders
    };

    res.status(200).json(responseData);
  } catch (error) {
    console.error('获取变更记录详情时出错:', error);
    res.status(500).json({ message: '获取变更记录详情失败', error: error.message });
  }
};

// 删除资产
exports.deleteAsset = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await Asset.destroy({ where: { id: id } });
    if (result) {
      res.status(204).send();
    } else {
      res.status(404).json({ message: '未找到指定资产' });
    }
  } catch (error) {
    console.error('删除资产时出错:', error);
    res.status(500).json({ message: '删除资产失败', error: error.message });
  }
};

/**
 * [新增] 获取单个资产的关联订单
 */
exports.getOrdersByAssetId = async (req, res) => {
  try {
    const { assetId } = req.params;

    // 查找该资产关联的所有订单
    const orders = await OrderHead.findAll({
      where: { asset_id: assetId },
      attributes: [
        'id', 'order_id', 'order_category', 'order_type', 'creation_method',
        'standard_amount', 'actual_amount', 'tax_amount', 'invoice_type',
        'payment_status', 'payment_method', 'payment_time', 'audit_status',
        'remark', 'change_details', 'created_at', 'updated_at'
      ],
      include: [
        {
          model: db.User,
          as: 'user',
          attributes: ['id', 'name', 'mobile', 'email']
        },
        {
          model: db.Enterprise,
          as: 'enterprise',
          attributes: ['id', 'enterprise_id', 'name']
        },
        {
          model: db.Employee,
          as: 'creator',
          attributes: ['id', 'name', 'mobile']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.status(200).json(orders);
  } catch (error) {
    console.error('获取资产关联订单时出错:', error);
    res.status(500).json({ message: '获取资产关联订单失败', error: error.message });
  }
};