-- 为asset_change_log表添加性能优化索引
-- 这些索引将显著提升查询性能，解决"Out of sort memory"错误

-- 1. 为change_date字段添加索引（最重要）
-- 这个索引将解决80%的性能问题，因为大部分查询都按change_date排序
ALTER TABLE `asset_change_log` ADD INDEX `idx_change_date` (`change_date` DESC);

-- 2. 为常用的组合查询添加复合索引
-- 这个索引优化按资产ID查询变更记录并按日期排序的场景
ALTER TABLE `asset_change_log` ADD INDEX `idx_asset_change_date` (`asset_id`, `change_date` DESC);

-- 3. 为创建时间添加索引（用于getAllAssetChanges的排序）
ALTER TABLE `asset_change_log` ADD INDEX `idx_created_at` (`createdAt` DESC);

-- 4. 为asset表的常用查询字段添加复合索引（如果还没有的话）
-- 检查是否已存在，如果不存在则添加
-- ALTER TABLE `asset` ADD INDEX `idx_enterprise_product` (`enterprise_id`, `product_id`);

-- 显示当前asset_change_log表的索引状态
SHOW INDEX FROM `asset_change_log`;
