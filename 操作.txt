
cd /var/www/customer_system/frontend
npm run build
pm2 restart customer-backend
pm2 logs customer-backend --lines 50

# 3. 如果构建失败，检查错误信息
npm run build 2>&1 | tee build.log
# 4. 构建完成后，dist目录会更新
# nginx会自动提供新的静态文件，无需重启

另外：1、服务都在阿里云服务器上PM2监测开启的，你无法帮我重启，
以后都不需要你帮我启动后端服务、前端的构建，你也无法执行任何命令，
让我自己手动在阿里云终端启动、执行即可！明白吗？
2、每次修改完代码，需要你验证一下文件的语法是否正确。

backend/logs customer-backend-out.log 


检查构建错误：
# 详细构建日志
npm run build --verbose
# 检查语法错误
npm run lint
# 检查依赖问题
npm install

# 方法1：使用PM2重启（推荐）
pm2 restart customer-backend
# 方法2：重启所有服务
pm2 restart all
# 方法3：如果PM2有问题，手动重启
pm2 delete customer-backend
pm2 start ecosystem.config.js
# 查看重启状态
pm2 list
pm2 logs customer-backend --lines 50



cd /var/www/customer_system
git add .                      # 添加所有修改
git commit -m "资产变更回滚控制"       # 提交到本地仓库
git push                       # 自动推送到远程 master
(首次：git push -u origin master)

下拉代码（从GitHub更新）
 git pull origin master
 
 强制覆盖本地（极端情况使用）​​
git reset --hard HEAD      # 丢弃所有未提交的修改
git pull origin master     # 强制同步远程代码
⚠️ ​会丢失全部未提交内容，仅当确定本地修改可废弃时使用
​克隆代码（完整下载仓库）​
<NAME_EMAIL>:970354474/minishop.git
git remote set-<NAME_EMAIL>:970354474/minishop.git