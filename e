
4|customer-backend  | 2025-07-31 20:34:10 +08:00: 数据库连接成功，且模型关联已加载。
4|customer-backend  | 2025-07-31 20:34:10 +08:00: 服务器正在端口 3002 上运行.
4|customer-backend  | 2025-07-31 20:34:35 +08:00: 获取资产详情时出错: Error
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at async Asset.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at async Asset.findOne (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1240:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at async Asset.findByPk (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1221:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     at async exports.getAssetById (/var/www/customer_system/backend/src/controllers/asset.controller.js:181:23) {
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   },
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:34:35 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:34:35 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   },
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:34:35 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:34:35 +08:00: }
4|customer-backend  | 2025-07-31 20:34:38 +08:00: 获取所有资产变更记录时出错: Error
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at async AssetChangeLog.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at async Promise.all (index 1)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at async AssetChangeLog.findAndCountAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1322:27)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     at async exports.getAllAssetChanges (/var/www/customer_system/backend/src/controllers/asset.controller.js:418:29) {
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name`, `asset`.`id` AS `asset.id`, `asset`.`asset_id` AS `asset.asset_id`, `asset->enterprise`.`id` AS `asset.enterprise.id`, `asset->enterprise`.`name` AS `asset.enterprise.name`, `asset->product`.`id` AS `asset.product.id`, `asset->product`.`product_name` AS `asset.product.product_name`, `asset->product`.`version_name` AS `asset.product.version_name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `AssetChangeLog`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `enterprise` AS `asset->enterprise` ON `asset`.`enterprise_id` = `asset->enterprise`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   },
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:34:38 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name`, `asset`.`id` AS `asset.id`, `asset`.`asset_id` AS `asset.asset_id`, `asset->enterprise`.`id` AS `asset.enterprise.id`, `asset->enterprise`.`name` AS `asset.enterprise.name`, `asset->product`.`id` AS `asset.product.id`, `asset->product`.`product_name` AS `asset.product.product_name`, `asset->product`.`version_name` AS `asset.product.version_name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `AssetChangeLog`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `enterprise` AS `asset->enterprise` ON `asset`.`enterprise_id` = `asset->enterprise`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   },
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name`, `asset`.`id` AS `asset.id`, `asset`.`asset_id` AS `asset.asset_id`, `asset->enterprise`.`id` AS `asset.enterprise.id`, `asset->enterprise`.`name` AS `asset.enterprise.name`, `asset->product`.`id` AS `asset.product.id`, `asset->product`.`product_name` AS `asset.product.product_name`, `asset->product`.`version_name` AS `asset.product.version_name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `AssetChangeLog`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `enterprise` AS `asset->enterprise` ON `asset`.`enterprise_id` = `asset->enterprise`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:34:38 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:34:38 +08:00: }
4|customer-backend  | 2025-07-31 20:34:42 +08:00: 获取资产详情时出错: Error
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at async Asset.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at async Asset.findOne (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1240:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at async Asset.findByPk (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1221:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     at async exports.getAssetById (/var/www/customer_system/backend/src/controllers/asset.controller.js:181:23) {
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   },
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:34:42 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:34:42 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   },
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:34:42 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:34:42 +08:00: }
4|customer-backend  | 2025-07-31 20:35:29 +08:00: 获取所有资产变更记录时出错: Error
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at async AssetChangeLog.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at async Promise.all (index 1)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at async AssetChangeLog.findAndCountAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1322:27)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     at async exports.getAllAssetChanges (/var/www/customer_system/backend/src/controllers/asset.controller.js:418:29) {
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name`, `asset`.`id` AS `asset.id`, `asset`.`asset_id` AS `asset.asset_id`, `asset->enterprise`.`id` AS `asset.enterprise.id`, `asset->enterprise`.`name` AS `asset.enterprise.name`, `asset->product`.`id` AS `asset.product.id`, `asset->product`.`product_name` AS `asset.product.product_name`, `asset->product`.`version_name` AS `asset.product.version_name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `AssetChangeLog`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `enterprise` AS `asset->enterprise` ON `asset`.`enterprise_id` = `asset->enterprise`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   },
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:35:29 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name`, `asset`.`id` AS `asset.id`, `asset`.`asset_id` AS `asset.asset_id`, `asset->enterprise`.`id` AS `asset.enterprise.id`, `asset->enterprise`.`name` AS `asset.enterprise.name`, `asset->product`.`id` AS `asset.product.id`, `asset->product`.`product_name` AS `asset.product.product_name`, `asset->product`.`version_name` AS `asset.product.version_name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `AssetChangeLog`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `enterprise` AS `asset->enterprise` ON `asset`.`enterprise_id` = `asset->enterprise`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   },
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name`, `asset`.`id` AS `asset.id`, `asset`.`asset_id` AS `asset.asset_id`, `asset->enterprise`.`id` AS `asset.enterprise.id`, `asset->enterprise`.`name` AS `asset.enterprise.name`, `asset->product`.`id` AS `asset.product.id`, `asset->product`.`product_name` AS `asset.product.product_name`, `asset->product`.`version_name` AS `asset.product.version_name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `AssetChangeLog`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `enterprise` AS `asset->enterprise` ON `asset`.`enterprise_id` = `asset->enterprise`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:35:29 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:35:29 +08:00: }
4|customer-backend  | 2025-07-31 20:35:31 +08:00: 获取资产详情时出错: Error
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at async Asset.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at async Asset.findOne (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1240:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at async Asset.findByPk (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1221:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     at async exports.getAssetById (/var/www/customer_system/backend/src/controllers/asset.controller.js:181:23) {
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   },
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:35:31 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:35:31 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   },
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_base` AS `user.commission_base`, `user`.`commission_extra` AS `user.commission_extra`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`related_order_ids` AS `changeLogs.related_order_ids`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`created_at` AS `orders.created_at`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer-backend  | 2025-07-31 20:35:31 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:35:31 +08:00: }


service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-BSGx6rS2.js:59  API Error:  获取资产详情失败
(匿名) @ index-BSGx6rS2.js:59
AssetDetail-Dhn0iOS6.js:3  Uncaught (in promise) xt
service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-BSGx6rS2.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-BSGx6rS2.js:59
AssetChangeList-lUlUYFzj.js:1  获取变更列表失败: xt
_ @ AssetChangeList-lUlUYFzj.js:1
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-BSGx6rS2.js:59  API Error:  获取资产详情失败
(匿名) @ index-BSGx6rS2.js:59
AssetChangeCreate-b06TIe3U.js:1  加载资产数据失败: xt
B @ AssetChangeCreate-b06TIe3U.js:1
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
inspector.js:7   GET https://service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
(匿名) @ index-BSGx6rS2.js:18
i @ asset-C79b2kwK.js:1
_ @ AssetChangeList-lUlUYFzj.js:1
(匿名) @ AssetChangeList-lUlUYFzj.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
index-BSGx6rS2.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-BSGx6rS2.js:59
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
(匿名) @ index-BSGx6rS2.js:18
i @ asset-C79b2kwK.js:1
_ @ AssetChangeList-lUlUYFzj.js:1
(匿名) @ AssetChangeList-lUlUYFzj.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
AssetChangeList-lUlUYFzj.js:1  获取变更列表失败: xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
_ @ AssetChangeList-lUlUYFzj.js:1
await in _
(匿名) @ AssetChangeList-lUlUYFzj.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
inspector.js:7 XHR 加载失败:GET "https://service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC".
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
(匿名) @ index-BSGx6rS2.js:18
i @ asset-C79b2kwK.js:1
_ @ AssetChangeList-lUlUYFzj.js:1
(匿名) @ AssetChangeList-lUlUYFzj.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
Zl.<computed> @ index-BSGx6rS2.js:23
(匿名) @ index-BSGx6rS2.js:18
$ @ product-Dc-05pVn.js:1
X @ AssetDetail-Dhn0iOS6.js:3
l @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
Zl.<computed> @ index-BSGx6rS2.js:23
(匿名) @ index-BSGx6rS2.js:18
u @ user-J4-8KuCU.js:1
X @ AssetDetail-Dhn0iOS6.js:3
l @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
Zl.<computed> @ index-BSGx6rS2.js:23
(匿名) @ index-BSGx6rS2.js:18
p @ enterprise-t0T120GG.js:1
X @ AssetDetail-Dhn0iOS6.js:3
l @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
inspector.js:7   GET https://service.bogoo.net/api/assets/1 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
(匿名) @ index-BSGx6rS2.js:18
c @ asset-C79b2kwK.js:1
_ @ useAssetData-CST_cgeQ.js:1
M @ AssetDetail-Dhn0iOS6.js:3
l @ AssetDetail-Dhn0iOS6.js:3
await in l
(匿名) @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
index-BSGx6rS2.js:59  API Error:  获取资产详情失败
(匿名) @ index-BSGx6rS2.js:59
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
(匿名) @ index-BSGx6rS2.js:18
c @ asset-C79b2kwK.js:1
_ @ useAssetData-CST_cgeQ.js:1
M @ AssetDetail-Dhn0iOS6.js:3
l @ AssetDetail-Dhn0iOS6.js:3
await in l
(匿名) @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
AssetDetail-Dhn0iOS6.js:3  Uncaught (in promise) xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
s2 @ index-BSGx6rS2.js:20
b @ index-BSGx6rS2.js:20
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BSGx6rS2.js:20
xhr @ index-BSGx6rS2.js:20
n0 @ index-BSGx6rS2.js:22
Promise.then
_request @ index-BSGx6rS2.js:23
request @ index-BSGx6rS2.js:22
(匿名) @ index-BSGx6rS2.js:18
c @ asset-C79b2kwK.js:1
_ @ useAssetData-CST_cgeQ.js:1
M @ AssetDetail-Dhn0iOS6.js:3
l @ AssetDetail-Dhn0iOS6.js:3
await in l
(匿名) @ AssetDetail-Dhn0iOS6.js:3
(匿名) @ index-BSGx6rS2.js:14
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
t.__weh.t.__weh @ index-BSGx6rS2.js:14
z1 @ index-BSGx6rS2.js:14
H1 @ index-BSGx6rS2.js:14
Promise.then
F1 @ index-BSGx6rS2.js:14
Xv @ index-BSGx6rS2.js:14
i.scheduler @ index-BSGx6rS2.js:14
d.scheduler @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
Wv @ index-BSGx6rS2.js:10
notify @ index-BSGx6rS2.js:10
trigger @ index-BSGx6rS2.js:10
set value @ index-BSGx6rS2.js:10
I @ index-BSGx6rS2.js:63
(匿名) @ index-BSGx6rS2.js:63
Promise.then
O @ index-BSGx6rS2.js:63
w @ index-BSGx6rS2.js:63
f @ index-BSGx6rS2.js:63
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
p @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
l$ @ index-BSGx6rS2.js:14
onClick @ index-BSGx6rS2.js:58
Yi @ index-BSGx6rS2.js:14
jo @ index-BSGx6rS2.js:14
n @ index-BSGx6rS2.js:18
inspector.js:7 XHR 加载失败:GET "https://service.bogoo.net/api/assets/1".