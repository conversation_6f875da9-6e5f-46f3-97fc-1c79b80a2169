# 回滚权限控制功能说明

## 功能概述

为了避免变更记录脱节的情况，实现了回滚权限控制功能。只有最新的变更记录才能进行回滚操作，非最新的变更记录回滚按钮将被置灰，并提供详细的提示信息。

## 业务逻辑

### 核心规则
- **只能回滚最新变更记录**：确保变更记录的时间顺序正确
- **按钮置灰提示**：非最新记录的回滚按钮置灰，避免用户误操作
- **智能提示**：显示最新变更单号，并支持一键复制

### 用户体验
1. **可回滚记录**：回滚按钮正常显示，悬浮提示"回滚到变更前状态"
2. **不可回滚记录**：回滚按钮置灰，悬浮提示详细说明原因
3. **复制功能**：提示中包含复制按钮，可快速复制最新变更单号

## 技术实现

### 后端实现

#### 1. 新增API接口
**文件**: `backend/src/controllers/asset.controller.js`

```javascript
// 获取资产的最新变更记录
exports.getLatestAssetChange = async (req, res) => {
  try {
    const { assetId } = req.params;
    
    const latestChange = await AssetChangeLog.findOne({
      where: { asset_id: assetId },
      attributes: ['id', 'asset_change_id', 'change_date', 'createdAt'],
      order: [['change_date', 'DESC'], ['createdAt', 'DESC']],
      limit: 1
    });

    if (!latestChange) {
      return res.status(404).json({ message: '该资产暂无变更记录' });
    }

    res.status(200).json(latestChange);
  } catch (error) {
    console.error('获取最新变更记录失败:', error);
    res.status(500).json({ message: '获取最新变更记录失败', error: error.message });
  }
};
```

#### 2. 路由配置
**文件**: `backend/src/routes/asset.routes.js`

```javascript
// 获取资产的最新变更记录
router.get('/:assetId/latest-change', employee.verifyEmployee, assetController.getLatestAssetChange);
```

### 前端实现

#### 1. API方法
**文件**: `frontend/src/api/asset.js`

```javascript
// 获取资产的最新变更记录
export function getLatestAssetChange(assetId) {
  return service({
    url: `${API_PATH}/${assetId}/latest-change`,
    method: 'get'
  });
}
```

#### 2. 组件实现

##### AssetChangeRecords.vue (资产详情页变更记录)

**核心功能**：
- 加载变更记录时同时获取最新变更记录
- 判断当前记录是否为最新记录
- 提供智能提示和复制功能

**关键方法**：
```javascript
// 判断是否可以回滚
const canRollback = (record) => {
  if (!latestChange.value) return false
  return record.id === latestChange.value.id
}

// 获取回滚按钮的提示文本
const getRollbackTooltip = (record) => {
  if (canRollback(record)) {
    return '回滚到变更前状态'
  }
  
  if (!latestChange.value) {
    return '暂无变更记录'
  }
  
  return `不是最新一条变更单，需先回滚最新一条变更记录，最新的一条变更单号为：${latestChange.value.asset_change_id}`
}

// 复制最新变更单号
const copyLatestChangeId = async () => {
  if (!latestChange.value) return
  
  try {
    await navigator.clipboard.writeText(latestChange.value.asset_change_id)
    ElMessage.success('变更单号已复制到剪贴板')
  } catch (error) {
    // 降级处理
    const textArea = document.createElement('textarea')
    textArea.value = latestChange.value.asset_change_id
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('变更单号已复制到剪贴板')
  }
}
```

##### AssetChangeList.vue (变更记录列表页)

**核心功能**：
- 为每个资产维护最新变更记录缓存
- 批量获取多个资产的最新变更记录
- 提供相同的权限控制和提示功能

**关键实现**：
```javascript
// 存储每个资产的最新变更记录
const latestChanges = ref(new Map())

// 加载每个资产的最新变更记录
const loadLatestChanges = async () => {
  const assetIds = [...new Set(tableData.value.map(record => record.asset?.id).filter(Boolean))]
  
  for (const assetId of assetIds) {
    try {
      const latestChange = await getLatestAssetChange(assetId)
      latestChanges.value.set(assetId, latestChange)
    } catch (error) {
      latestChanges.value.set(assetId, null)
    }
  }
}

// 获取指定资产的最新变更记录
const getLatestChangeForAsset = (assetId) => {
  return latestChanges.value.get(assetId)
}
```

#### 3. UI组件设计

**回滚按钮**：
```vue
<el-tooltip
  placement="top"
  :disabled="canRollback(row)"
  raw-content
>
  <template #content>
    <div v-if="!canRollback(row)" class="rollback-tooltip">
      <div>{{ getRollbackTooltip(row) }}</div>
      <div v-if="latestChange" class="copy-section">
        <el-button 
          size="small" 
          type="primary" 
          @click="copyLatestChangeId"
          class="copy-btn"
        >
          复制变更单号
        </el-button>
      </div>
    </div>
  </template>
  <el-button
    size="small"
    type="warning"
    :disabled="readonly || !canRollback(row)"
    @click="rollbackToChange(row)"
  >
    回滚
  </el-button>
</el-tooltip>
```

## 功能特点

### 1. 安全性
- **防止数据不一致**：确保回滚操作按正确顺序进行
- **权限控制**：只有最新记录才能回滚
- **用户友好**：清晰的视觉反馈和提示

### 2. 易用性
- **智能提示**：详细说明为什么不能回滚
- **一键复制**：快速复制最新变更单号
- **视觉反馈**：按钮置灰状态清晰可见

### 3. 性能优化
- **批量获取**：列表页面批量获取最新变更记录
- **缓存机制**：避免重复请求
- **异步处理**：不阻塞主要数据加载

### 4. 兼容性
- **浏览器兼容**：复制功能支持现代浏览器和传统浏览器
- **响应式设计**：在不同屏幕尺寸下正常显示
- **向后兼容**：不影响现有功能

## 使用场景

### 场景1：资产详情页
用户在查看资产详情时，可以看到所有变更记录，但只能回滚最新的一条记录。

### 场景2：变更记录列表页
用户在变更记录列表中，可以看到所有变更记录的回滚状态，快速识别哪些可以回滚。

### 场景3：错误提示
当用户尝试回滚非最新记录时，系统会提供清晰的提示，告知需要先回滚哪条记录。

## 测试建议

1. **基础功能测试**：
   - 创建多条变更记录
   - 验证只有最新记录可以回滚
   - 测试回滚后状态更新

2. **UI交互测试**：
   - 验证按钮置灰状态
   - 测试悬浮提示显示
   - 验证复制功能

3. **边界情况测试**：
   - 无变更记录的资产
   - 只有一条变更记录的资产
   - 网络异常情况处理

4. **性能测试**：
   - 大量变更记录的加载性能
   - 多个资产的批量处理性能

## 总结

回滚权限控制功能通过技术手段确保了业务逻辑的正确性，同时提供了良好的用户体验。该功能有效防止了变更记录脱节的问题，提高了系统的数据一致性和可靠性。
