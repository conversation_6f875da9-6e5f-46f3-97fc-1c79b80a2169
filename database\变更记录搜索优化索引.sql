-- 资产变更记录搜索优化索引
-- 用于提升变更记录列表页面的搜索和排序性能

-- 1. 变更ID索引（支持模糊搜索）
CREATE INDEX idx_asset_change_logs_change_id ON asset_change_logs(asset_change_id);

-- 2. 变更日期索引（支持排序和日期范围查询）
CREATE INDEX idx_asset_change_logs_change_date ON asset_change_logs(change_date DESC);

-- 3. 资产ID索引（支持按资产过滤）
CREATE INDEX idx_asset_change_logs_asset_id ON asset_change_logs(asset_id);

-- 4. 制单人索引（支持按制单人过滤）
CREATE INDEX idx_asset_change_logs_creator_id ON asset_change_logs(creator_id);

-- 5. 创建时间索引（支持排序）
CREATE INDEX idx_asset_change_logs_created_at ON asset_change_logs(createdAt DESC);

-- 6. 复合索引：变更日期 + 创建时间（优化默认排序）
CREATE INDEX idx_asset_change_logs_date_created ON asset_change_logs(change_date DESC, createdAt DESC);

-- 7. 复合索引：资产ID + 变更日期（优化按资产查询变更记录）
CREATE INDEX idx_asset_change_logs_asset_date ON asset_change_logs(asset_id, change_date DESC);

-- 8. 资产表的资产ID索引（如果不存在）
-- CREATE INDEX idx_assets_asset_id ON assets(asset_id);

-- 9. 员工表的姓名索引（支持制单人模糊搜索）
-- CREATE INDEX idx_employees_name ON employees(name);

-- 查看当前索引状态
SHOW INDEX FROM asset_change_logs;

-- 查看表结构
DESCRIBE asset_change_logs;

-- 分析查询性能（示例）
-- EXPLAIN SELECT * FROM asset_change_logs 
-- WHERE asset_change_id LIKE '%CH%' 
-- ORDER BY change_date DESC, createdAt DESC 
-- LIMIT 20;
